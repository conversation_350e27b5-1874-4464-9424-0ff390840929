import pandas as pd
import numpy as np
import logging
import os

logger = logging.getLogger(__name__)

class DataLoader:
    """负责加载和合并数据集的类"""
    
    def __init__(self, config):
        """
        初始化数据加载器
        
        参数:
            config (dict): 包含数据路径的配置字典
        """
        self.config = config
        self.data = {}
        
    def load_data(self):
        """加载所有数据集"""
        for dataset_name, path in self.config['datasets'].items():
            logger.info(f"加载数据集: {dataset_name}")
            try:
                self.data[dataset_name] = pd.read_csv(path)
                logger.info(f"成功加载 {dataset_name}, 形状: {self.data[dataset_name].shape}")
            except Exception as e:
                logger.error(f"加载 {dataset_name} 失败: {str(e)}")
                raise
        
        return self.data
    
    def get_train_test_data(self):
        """返回训练集和测试集"""
        if 'application_train' not in self.data or 'application_test' not in self.data:
            raise ValueError("请先调用load_data()方法加载数据")
        
        return self.data['application_train'], self.data['application_test']