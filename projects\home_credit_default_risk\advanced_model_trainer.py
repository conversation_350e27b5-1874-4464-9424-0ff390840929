import pandas as pd
import numpy as np
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from sklearn.metrics import roc_auc_score
import logging

logger = logging.getLogger(__name__)


class AdvancedModelTrainer:
    """高级模型训练器，支持多模型堆叠和融合"""

    def __init__(self, config):
        """
        初始化高级模型训练器

        参数:
            config (dict): 包含模型参数的配置字典
        """
        self.config = config
        self.base_models = []
        self.meta_model = None
        self.feature_importances = None
        self.model_weights = {}

    def train_base_models(self, X, y, test_df):
        """
        训练多个基模型并生成折外预测

        参数:
            X (DataFrame): 训练特征
            y (Series): 目标变量
            test_df (DataFrame): 测试特征

        返回:
            tuple: (折外预测矩阵, 测试集预测矩阵)
        """
        logger.info("开始训练多个基模型")

        # 设置交叉验证
        n_folds = self.config["training"]["n_folds"]
        kf = StratifiedKFold(
            n_splits=n_folds,
            shuffle=True,
            random_state=self.config["training"]["random_state"],
        )

        # 定义模型配置
        models_config = {
            "xgb": {
                "model": XGBClassifier,
                "params": {
                    "objective": "binary:logistic",
                    "eval_metric": "auc",
                    "max_depth": 6,
                    "learning_rate": 0.02,
                    "n_estimators": 2000,
                    "subsample": 0.8,
                    "colsample_bytree": 0.8,
                    "min_child_weight": 3,
                    "reg_alpha": 0.1,
                    "reg_lambda": 0.1,
                    "random_state": 42,
                    "n_jobs": -1,
                },
            },
            "lgb": {
                "model": LGBMClassifier,
                "params": {
                    "objective": "binary",
                    "metric": "auc",
                    "boosting_type": "gbdt",
                    "num_leaves": 31,
                    "learning_rate": 0.02,
                    "feature_fraction": 0.9,
                    "bagging_fraction": 0.8,
                    "bagging_freq": 5,
                    "min_child_samples": 20,
                    "reg_alpha": 0.1,
                    "reg_lambda": 0.1,
                    "verbose": -1,
                    "random_state": 42,
                    "n_estimators": 2000,
                    "n_jobs": -1,
                },
            },
        }

        # 初始化预测矩阵
        n_models = len(models_config)
        oof_preds = np.zeros((X.shape[0], n_models))
        test_preds = np.zeros((test_df.shape[0], n_models))
        feature_importance_df = pd.DataFrame()

        # 训练每个模型
        for model_idx, (model_name, model_config) in enumerate(models_config.items()):
            logger.info(f"训练 {model_name.upper()} 模型")

            model_oof = np.zeros(X.shape[0])
            model_test = np.zeros(test_df.shape[0])

            # 交叉验证训练
            for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
                logger.info(f"{model_name.upper()} - 折 {fold+1}/{n_folds}")

                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

                # 创建并训练模型
                model = model_config["model"](**model_config["params"])

                if model_name == "lgb":
                    model.fit(X_train, y_train, eval_set=[(X_val, y_val)])
                else:  # xgb
                    model.fit(
                        X_train, y_train, eval_set=[(X_val, y_val)], verbose=False
                    )

                # 保存模型
                self.base_models.append((model_name, fold, model))

                # 生成预测
                model_oof[val_idx] = model.predict_proba(X_val)[:, 1]
                model_test += model.predict_proba(test_df)[:, 1] / n_folds

                # 计算验证集AUC
                fold_auc = roc_auc_score(y_val, model_oof[val_idx])
                logger.info(f"{model_name.upper()} - 折 {fold+1} AUC: {fold_auc:.6f}")

                # 记录特征重要性
                fold_importance = pd.DataFrame()
                fold_importance["feature"] = X.columns
                fold_importance["importance"] = model.feature_importances_
                fold_importance["model"] = model_name
                fold_importance["fold"] = fold + 1
                feature_importance_df = pd.concat(
                    [feature_importance_df, fold_importance], axis=0
                )

            # 保存模型预测
            oof_preds[:, model_idx] = model_oof
            test_preds[:, model_idx] = model_test

            # 计算模型整体AUC
            model_auc = roc_auc_score(y, model_oof)
            logger.info(f"{model_name.upper()} 整体AUC: {model_auc:.6f}")
            self.model_weights[model_name] = model_auc

        # 保存特征重要性
        self.feature_importances = (
            feature_importance_df.groupby("feature")["importance"]
            .mean()
            .sort_values(ascending=False)
        )

        return oof_preds, test_preds

    def train_meta_model(self, oof_preds, y):
        """
        训练元模型

        参数:
            oof_preds (array): 基模型的折外预测矩阵
            y (Series): 目标变量
        """
        logger.info("开始训练元模型")

        # 训练逻辑回归作为元模型
        self.meta_model = LogisticRegression(**self.config["logistic_params"])
        self.meta_model.fit(oof_preds, y)

        # 计算元模型AUC
        meta_preds = self.meta_model.predict_proba(oof_preds)[:, 1]
        meta_auc = roc_auc_score(y, meta_preds)
        logger.info(f"元模型AUC: {meta_auc:.6f}")

    def generate_submission(self, test_preds, test_ids, output_path="submission.csv"):
        """
        生成提交文件

        参数:
            test_preds (array): 测试集预测矩阵
            test_ids (array): 测试集ID
            output_path (str): 输出文件路径
        """
        logger.info("生成提交文件")

        # 使用元模型进行最终预测
        if self.meta_model is not None:
            final_preds = self.meta_model.predict_proba(test_preds)[:, 1]
        else:
            # 如果没有元模型，使用加权平均
            weights = np.array(
                [self.model_weights.get("xgb", 0.5), self.model_weights.get("lgb", 0.5)]
            )
            weights = weights / weights.sum()
            final_preds = np.average(test_preds, axis=1, weights=weights)

        # 创建提交DataFrame
        submission = pd.DataFrame({"SK_ID_CURR": test_ids, "TARGET": final_preds})

        # 保存到CSV
        submission.to_csv(output_path, index=False)
        logger.info(f"提交文件已保存到 {output_path}")

        return submission
