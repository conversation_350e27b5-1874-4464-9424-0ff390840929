# Home Credit Default Risk

## 项目概述

本项目旨在预测客户的还款能力，帮助Home Credit做出更好的贷款决策。使用多源数据，包括信用局数据和先前申请记录，构建一个机器学习模型来预测客户是否会出现还款困难。

## 数据集

数据集包含以下文件：

- `application_{train|test}.csv`: 主表，包含每个贷款申请的静态数据
- `bureau.csv`: 申请者在其他金融机构的信用记录
- `bureau_balance.csv`: 信用记录的月度余额
- `previous_application.csv`: Home Credit先前贷款申请
- `POS_CASH_balance.csv`: 先前POS和现金贷款的月度余额
- `credit_card_balance.csv`: 先前信用卡的月度余额
- `installments_payments.csv`: 先前贷款的还款历史

## 项目结构

```
home_credit_default_risk/
├── config.yaml           # 配置文件
├── main.py               # 主程序入口
├── data_loader.py        # 数据加载模块
├── preprocessor.py       # 数据预处理模块
├── feature_engineering.py # 特征工程模块
├── model_trainer.py      # 模型训练模块
├── logs/                 # 日志目录
└── README.md             # 项目说明
```

## 解决方案

本项目采用以下方法解决Home Credit Default Risk问题：

1. **数据预处理**：处理缺失值、编码分类变量
2. **特征工程**：从多源数据中聚合统计信息，创建基于领域知识的新特征
3. **混合建模**：使用XGBoost作为基模型，逻辑回归作为元模型进行堆叠
4. **交叉验证**：使用5折交叉验证评估模型性能
5. **模型融合**：结合基模型和元模型的预测提高性能

## 使用方法

### 安装依赖

```bash
pip install pandas numpy scikit-learn xgboost pyyaml
```

### 运行项目

```bash
python main.py --config config.yaml --output submission.csv
```

### 参数说明

- `--config`: 配置文件路径，默认为`config.yaml`
- `--output`: 提交文件输出路径，默认为`submission.csv`
- `--log_dir`: 日志目录，默认为`logs`

## 性能评估

模型使用ROC AUC作为评估指标，通过交叉验证和Kaggle提交评估性能。

## 作者

[Your Name]

## 许可证

[License Information]