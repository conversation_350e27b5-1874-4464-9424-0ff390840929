import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)


class FeatureEngineer:
    """负责特征工程的类"""

    def __init__(self, data_dict):
        """
        初始化特征工程器

        参数:
            data_dict (dict): 包含所有数据集的字典
        """
        self.data = data_dict

    def aggregate_bureau(self, main_df):
        """
        聚合bureau表数据

        参数:
            main_df (DataFrame): 主表数据

        返回:
            DataFrame: 添加了bureau聚合特征的主表
        """
        logger.info("聚合bureau表数据")

        if "bureau" not in self.data:
            logger.warning("bureau表不存在，跳过聚合")
            return main_df

        bureau = self.data["bureau"]

        # 数值列聚合
        num_aggregations = {
            "DAYS_CREDIT": ["min", "max", "mean", "var"],
            "DAYS_CREDIT_ENDDATE": ["min", "max", "mean"],
            "DAYS_CREDIT_UPDATE": ["mean"],
            "CREDIT_DAY_OVERDUE": ["max", "mean"],
            "AMT_CREDIT_MAX_OVERDUE": ["mean"],
            "AMT_CREDIT_SUM": ["max", "mean", "sum"],
            "AMT_CREDIT_SUM_DEBT": ["max", "mean", "sum"],
            "AMT_CREDIT_SUM_OVERDUE": ["mean"],
            "AMT_CREDIT_SUM_LIMIT": ["mean", "sum"],
            "AMT_ANNUITY": ["max", "mean"],
            "CNT_CREDIT_PROLONG": ["sum"],
        }

        # 分类列聚合 - 只计算计数
        cat_aggregations = {}
        for cat in bureau.select_dtypes(include=["object"]).columns:
            if cat != "SK_ID_CURR":  # 排除ID列
                cat_aggregations[cat] = ["count"]

        bureau_agg = bureau.groupby("SK_ID_CURR").agg(
            {**num_aggregations, **cat_aggregations}
        )
        bureau_agg.columns = [
            "_".join(col).strip() for col in bureau_agg.columns.values
        ]

        # 合并到主表
        main_df = main_df.merge(bureau_agg, on="SK_ID_CURR", how="left")

        logger.info(f"bureau聚合完成，添加了{bureau_agg.shape[1]}个特征")
        return main_df

    def aggregate_bureau_balance(self, main_df):
        """聚合bureau_balance表数据"""
        logger.info("聚合bureau_balance表数据")

        if "bureau_balance" not in self.data or "bureau" not in self.data:
            logger.warning("bureau_balance表或bureau表不存在，跳过聚合")
            return main_df

        bureau = self.data["bureau"]
        bureau_balance = self.data["bureau_balance"]

        # 按SK_ID_BUREAU聚合
        bb_aggregations = {"MONTHS_BALANCE": ["min", "max", "mean"]}

        bb_agg = bureau_balance.groupby("SK_ID_BUREAU").agg(bb_aggregations)
        bb_agg.columns = ["_".join(col).strip() for col in bb_agg.columns.values]

        # 合并到bureau表
        bureau = bureau.merge(bb_agg, on="SK_ID_BUREAU", how="left")

        # 再按SK_ID_CURR聚合
        bureau_agg = bureau.groupby("SK_ID_CURR").agg(
            {
                "MONTHS_BALANCE_min": ["min"],
                "MONTHS_BALANCE_max": ["max"],
                "MONTHS_BALANCE_mean": ["mean"],
            }
        )
        bureau_agg.columns = [
            "_".join(col).strip() for col in bureau_agg.columns.values
        ]

        # 合并到主表
        main_df = main_df.merge(bureau_agg, on="SK_ID_CURR", how="left")

        logger.info(f"bureau_balance聚合完成，添加了{bureau_agg.shape[1]}个特征")
        return main_df

    def aggregate_previous_applications(self, main_df):
        """聚合previous_application表数据"""
        logger.info("聚合previous_application表数据")

        if "previous_application" not in self.data:
            logger.warning("previous_application表不存在，跳过聚合")
            return main_df

        prev = self.data["previous_application"]

        # 数值列聚合
        num_aggregations = {
            "AMT_ANNUITY": ["min", "max", "mean"],
            "AMT_APPLICATION": ["min", "max", "mean"],
            "AMT_CREDIT": ["min", "max", "mean"],
            "AMT_DOWN_PAYMENT": ["min", "max", "mean"],
            "AMT_GOODS_PRICE": ["min", "max", "mean"],
            "HOUR_APPR_PROCESS_START": ["min", "max", "mean"],
            "DAYS_DECISION": ["min", "max", "mean"],
            "CNT_PAYMENT": ["mean", "sum"],
        }

        # 分类列聚合
        cat_aggregations = {}
        for cat in prev.select_dtypes(include=["object"]).columns:
            if cat != "SK_ID_CURR":  # 排除ID列
                cat_aggregations[cat] = ["count"]

        prev_agg = prev.groupby("SK_ID_CURR").agg(
            {**num_aggregations, **cat_aggregations}
        )
        prev_agg.columns = ["_".join(col).strip() for col in prev_agg.columns.values]

        # 创建新特征
        prev_agg["PREV_APPLICATIONS_COUNT"] = prev.groupby("SK_ID_CURR").size()

        # 合并到主表
        main_df = main_df.merge(prev_agg, on="SK_ID_CURR", how="left")

        logger.info(f"previous_application聚合完成，添加了{prev_agg.shape[1]}个特征")
        return main_df

    def create_domain_features(self, main_df):
        """创建基于领域知识的特征"""
        logger.info("创建领域特征")

        # 债务收入比
        if (
            "AMT_CREDIT_SUM_DEBT_sum" in main_df.columns
            and "AMT_INCOME_TOTAL" in main_df.columns
        ):
            main_df["DEBT_TO_INCOME"] = (
                main_df["AMT_CREDIT_SUM_DEBT_sum"] / main_df["AMT_INCOME_TOTAL"]
            )
            main_df["DEBT_TO_INCOME"] = main_df["DEBT_TO_INCOME"].replace(
                [np.inf, -np.inf], np.nan
            )

        # 信用额度与收入比
        if "AMT_CREDIT" in main_df.columns and "AMT_INCOME_TOTAL" in main_df.columns:
            main_df["CREDIT_TO_INCOME"] = (
                main_df["AMT_CREDIT"] / main_df["AMT_INCOME_TOTAL"]
            )
            main_df["CREDIT_TO_INCOME"] = main_df["CREDIT_TO_INCOME"].replace(
                [np.inf, -np.inf], np.nan
            )

        # 年龄特征（DAYS_BIRTH是负值，表示出生至今的天数）
        if "DAYS_BIRTH" in main_df.columns:
            main_df["AGE_YEARS"] = abs(main_df["DAYS_BIRTH"]) / 365.25

        # 就业时间特征（DAYS_EMPLOYED是负值，表示就业至今的天数）
        if "DAYS_EMPLOYED" in main_df.columns:
            main_df["EMPLOYMENT_YEARS"] = abs(main_df["DAYS_EMPLOYED"]) / 365.25
            # 处理异常值（如999年的就业时间）
            main_df["EMPLOYMENT_YEARS"].replace(365243.0 / 365.25, np.nan, inplace=True)

        # 就业收入比
        if (
            "EMPLOYMENT_YEARS" in main_df.columns
            and "AMT_INCOME_TOTAL" in main_df.columns
        ):
            main_df["INCOME_PER_EMPLOYMENT_YEAR"] = (
                main_df["AMT_INCOME_TOTAL"] / main_df["EMPLOYMENT_YEARS"]
            )
            main_df["INCOME_PER_EMPLOYMENT_YEAR"] = main_df[
                "INCOME_PER_EMPLOYMENT_YEAR"
            ].replace([np.inf, -np.inf], np.nan)

        logger.info("领域特征创建完成")
        return main_df

    def engineer_features(self, main_df):
        """执行所有特征工程步骤"""
        logger.info("开始特征工程")

        # 聚合辅助表
        main_df = self.aggregate_bureau(main_df)
        main_df = self.aggregate_bureau_balance(main_df)
        main_df = self.aggregate_previous_applications(main_df)

        # 创建领域特征
        main_df = self.create_domain_features(main_df)

        logger.info(f"特征工程完成，最终特征数量: {main_df.shape[1]}")
        return main_df
