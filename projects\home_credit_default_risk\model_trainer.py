import pandas as pd
import numpy as np
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from sklearn.metrics import roc_auc_score
import logging

logger = logging.getLogger(__name__)


class ModelTrainer:
    """负责模型训练和预测的类"""

    def __init__(self, config):
        """
        初始化模型训练器

        参数:
            config (dict): 包含模型参数的配置字典
        """
        self.config = config
        self.base_models = []
        self.meta_model = None
        self.feature_importances = None

    def train_base_models(self, X, y, test_df):
        """
        训练基模型并生成折外预测

        参数:
            X (DataFrame): 训练特征
            y (Series): 目标变量
            test_df (DataFrame): 测试特征

        返回:
            tuple: (折外预测, 测试集预测)
        """
        logger.info("开始训练基模型")

        # 设置交叉验证
        n_folds = self.config["training"]["n_folds"]
        kf = StratifiedKFold(
            n_splits=n_folds,
            shuffle=True,
            random_state=self.config["training"]["random_state"],
        )

        # 初始化折外预测和测试集预测
        oof_preds = np.zeros(X.shape[0])
        test_preds = np.zeros(test_df.shape[0])
        feature_importance_df = pd.DataFrame()

        # 交叉验证训练
        for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
            logger.info(f"训练折 {fold+1}/{n_folds}")

            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

            # 训练XGBoost模型
            model = XGBClassifier(**self.config["xgboost_params"])
            model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)

            # 保存模型
            self.base_models.append(model)

            # 生成预测
            oof_preds[val_idx] = model.predict_proba(X_val)[:, 1]
            test_preds += model.predict_proba(test_df)[:, 1] / n_folds

            # 计算验证集AUC
            fold_auc = roc_auc_score(y_val, oof_preds[val_idx])
            logger.info(f"折 {fold+1} AUC: {fold_auc:.6f}")

            # 记录特征重要性
            fold_importance = pd.DataFrame()
            fold_importance["feature"] = X.columns
            fold_importance["importance"] = model.feature_importances_
            fold_importance["fold"] = fold + 1
            feature_importance_df = pd.concat(
                [feature_importance_df, fold_importance], axis=0
            )

        # 计算整体AUC
        overall_auc = roc_auc_score(y, oof_preds)
        logger.info(f"整体AUC: {overall_auc:.6f}")

        # 保存特征重要性
        self.feature_importances = (
            feature_importance_df.groupby("feature")["importance"]
            .mean()
            .sort_values(ascending=False)
        )

        return oof_preds, test_preds

    def train_meta_model(self, oof_preds, y):
        """
        训练元模型

        参数:
            oof_preds (array): 基模型的折外预测
            y (Series): 目标变量
        """
        logger.info("开始训练元模型")

        # 将折外预测转换为特征矩阵
        X_meta = oof_preds.reshape(-1, 1)

        # 训练逻辑回归作为元模型
        self.meta_model = LogisticRegression(**self.config["logistic_params"])
        self.meta_model.fit(X_meta, y)

        # 计算元模型AUC
        meta_preds = self.meta_model.predict_proba(X_meta)[:, 1]
        meta_auc = roc_auc_score(y, meta_preds)
        logger.info(f"元模型AUC: {meta_auc:.6f}")

    def generate_submission(self, test_preds, test_ids, output_path="submission.csv"):
        """
        生成提交文件

        参数:
            test_preds (array): 测试集预测
            test_ids (array): 测试集ID
            output_path (str): 输出文件路径
        """
        logger.info("生成提交文件")

        # 如果有元模型，使用元模型进行最终预测
        if self.meta_model is not None:
            test_preds = test_preds.reshape(-1, 1)
            final_preds = self.meta_model.predict_proba(test_preds)[:, 1]
        else:
            final_preds = test_preds

        # 创建提交DataFrame
        submission = pd.DataFrame({"SK_ID_CURR": test_ids, "TARGET": final_preds})

        # 保存到CSV
        submission.to_csv(output_path, index=False)
        logger.info(f"提交文件已保存到 {output_path}")

        return submission
